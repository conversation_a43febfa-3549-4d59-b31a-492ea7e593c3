#!/usr/bin/env python3
"""
测试 SwanLab API 调用逻辑
"""

import json
import sys
import os
from unittest.mock import patch, Mock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_utils import load_config_from_ini
from finetune_utils import get_swanlab_experiment_id


def test_swanlab_api_logic():
    """测试 SwanLab API 响应解析逻辑"""
    
    # 模拟你提供的响应体
    mock_response = {
        "code": 0,
        "message": "success",
        "data": {
            "id": 1,
            "name": "llamafactory",
            "description": None,
            "sum": 3,
            "charts": 1,
            "more": "",
            "pinned_opened": 1,
            "hidden_opened": 0,
            "version": "0.6.8",
            "create_time": "2025-08-18T06:26:12.520435+00:00",
            "update_time": "2025-08-18T08:27:58.570398+00:00",
            "logdir": "/app/ma<PERSON><PERSON>/SwanLab-Dashboard/swanlog",
            "experiments": [
                {
                    "id": 1,
                    "run_id": "run-20250818_142612-aji0y6dpbhsahxthv3a3z",
                    "name": "test007",
                    "description": None,
                    "sort": 1,
                    "status": 1,
                    "show": 1,
                    "light": "#9cbe5d",
                    "dark": "#9cbe5d",
                    "pinned_opened": 1,
                    "hidden_opened": 0,
                    "more": "",
                    "version": "0.6.8",
                    "create_time": "2025-08-18T06:26:12.676217+00:00",
                    "finish_time": "2025-08-18T06:26:14.759654+00:00",
                    "update_time": "2025-08-18T06:26:14.759663+00:00",
                    "experiment_id": 1,
                },
                {
                    "id": 2,
                    "run_id": "run-20250818_142612-aji0y6dpbhsahxthv3a3z",
                    "name": "finetune_job_12345",
                    "description": None,
                    "sort": 2,
                    "status": 1,
                    "show": 1,
                    "light": "#5d9cbe",
                    "dark": "#5d9cbe",
                    "pinned_opened": 1,
                    "hidden_opened": 0,
                    "more": "",
                    "version": "0.6.8",
                    "create_time": "2025-08-18T06:26:12.676217+00:00",
                    "finish_time": "2025-08-18T06:26:14.759654+00:00",
                    "update_time": "2025-08-18T06:26:14.759663+00:00",
                    "experiment_id": 2,
                }
            ]
        }
    }
    
    print("=== 测试 SwanLab API 响应解析逻辑 ===\n")
    
    # 模拟 exec_task 数据
    exec_task = {"finetune_job_id": 12345}
    
    # 测试逻辑
    swanlab_job_id = None
    
    # 检查响应是否成功
    if mock_response.get("code") == 0 and mock_response.get("message") == "success":
        print("✅ API 响应成功")
        
        # 解析 experiments 数据
        experiments = mock_response.get("data", {}).get("experiments", [])
        finetune_job_name = "finetune_job_" + str(exec_task["finetune_job_id"])
        
        print(f"查找实验名称: {finetune_job_name}")
        print(f"可用的实验: {[exp.get('name') for exp in experiments]}")
        
        # 根据名称查找对应的实验 ID
        for experiment in experiments:
            if experiment.get("name") == finetune_job_name:
                swanlab_job_id = experiment.get("id")
                print(f"✅ 找到匹配的实验 ID: {swanlab_job_id}")
                break
        
        if swanlab_job_id:
            print(f"✅ 成功获取 swanlab 任务 ID: {swanlab_job_id}")
            print(f"✅ 准备写入数据库: swanlabs_job_id = {swanlab_job_id}, finetune_job_id = {exec_task['finetune_job_id']}")
        else:
            print(f"❌ 未找到匹配的实验，名称: {finetune_job_name}")
    else:
        print(f"❌ API 响应失败，code: {mock_response.get('code')}, message: {mock_response.get('message')}")
    
    # 测试不匹配的情况
    print("\n=== 测试不匹配的情况 ===")
    exec_task_no_match = {"finetune_job_id": 99999}
    finetune_job_name_no_match = "finetune_job_" + str(exec_task_no_match["finetune_job_id"])
    
    swanlab_job_id_no_match = None
    experiments = mock_response.get("data", {}).get("experiments", [])
    
    for experiment in experiments:
        if experiment.get("name") == finetune_job_name_no_match:
            swanlab_job_id_no_match = experiment.get("id")
            break
    
    if swanlab_job_id_no_match:
        print(f"❌ 意外找到匹配: {swanlab_job_id_no_match}")
    else:
        print(f"✅ 正确处理不匹配情况，名称: {finetune_job_name_no_match}")


def test_config_loading():
    """测试配置加载"""
    print("\n=== 测试 SwanLab 配置加载 ===")
    
    try:
        swanlab_config = load_config_from_ini("Swanlab")
        swanlab_host = swanlab_config.get("swanlab_host")
        swanlab_port = swanlab_config.get("swanlab_port")
        api_url = f"http://{swanlab_host}:{swanlab_port}/api/v1/project"
        
        print(f"SwanLab 主机: {swanlab_host}")
        print(f"SwanLab 端口: {swanlab_port}")
        print(f"API URL: {api_url}")
        print("✅ 配置加载成功")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")


def test_get_swanlab_experiment_id_function():
    """测试 get_swanlab_experiment_id 函数"""
    print("\n=== 测试 get_swanlab_experiment_id 函数 ===")

    # 模拟响应数据
    mock_response_data = {
        "code": 0,
        "message": "success",
        "data": {
            "experiments": [
                {"id": 1, "name": "test007"},
                {"id": 2, "name": "finetune_job_12345"},
                {"id": 3, "name": "another_experiment"}
            ]
        }
    }

    # 模拟成功的 HTTP 响应
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = mock_response_data

    with patch('finetune_utils.requests.get', return_value=mock_response):
        # 测试找到匹配的实验
        result = get_swanlab_experiment_id(12345)
        if result == 2:
            print("✅ 成功找到匹配的实验 ID: 2")
        else:
            print(f"❌ 期望得到 2，但得到 {result}")

        # 测试未找到匹配的实验
        result = get_swanlab_experiment_id(99999)
        if result is None:
            print("✅ 正确处理未找到匹配实验的情况")
        else:
            print(f"❌ 期望得到 None，但得到 {result}")

    # 测试 API 失败的情况
    mock_response_fail = Mock()
    mock_response_fail.status_code = 500
    mock_response_fail.text = "Internal Server Error"

    with patch('finetune_utils.requests.get', return_value=mock_response_fail):
        result = get_swanlab_experiment_id(12345)
        if result is None:
            print("✅ 正确处理 API 失败的情况")
        else:
            print(f"❌ 期望得到 None，但得到 {result}")

    # 测试网络异常的情况
    with patch('finetune_utils.requests.get', side_effect=Exception("Network error")):
        result = get_swanlab_experiment_id(12345)
        if result is None:
            print("✅ 正确处理网络异常的情况")
        else:
            print(f"❌ 期望得到 None，但得到 {result}")


def main():
    """主测试函数"""
    test_config_loading()
    test_swanlab_api_logic()
    test_get_swanlab_experiment_id_function()
    print("\n🎉 所有测试完成!")


if __name__ == "__main__":
    main()
