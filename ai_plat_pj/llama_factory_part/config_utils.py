"""
配置工具模块
提供从 INI 文件加载配置的通用功能
"""

import configparser
import os
import pathlib
from typing import Dict, Any


def load_config_from_ini(part: str, config_file_path: str = None) -> Dict[str, Any]:
    """
    从 INI 文件加载配置并转换为字典
    
    Args:
        part (str): 配置文件中的节名称
        config_file_path (str, optional): 配置文件路径，如果不提供则使用默认路径
        
    Returns:
        Dict[str, Any]: 配置字典，值会根据类型自动转换
        
    Raises:
        FileNotFoundError: 当配置文件不存在时
        KeyError: 当指定的节不存在时
    """
    config_parser = configparser.ConfigParser(interpolation=None)  # 禁用插值以避免%字符问题
    
    # 如果没有提供配置文件路径，使用默认路径（当前文件所在目录的config.ini）
    if config_file_path is None:
        current_dir = pathlib.Path(__file__).parent.absolute()
        config_file = os.path.join(current_dir, 'config.ini')
    else:
        config_file = config_file_path

    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    config_parser.read(config_file, encoding='utf-8')
    
    # 检查指定的节是否存在
    if part not in config_parser:
        raise KeyError(f"配置文件中不存在节: {part}")

    # 将指定节中的所有配置项转换为字典
    config_dict = {}
    for key, value in config_parser[part].items():
        # 尝试将数值类型的字符串转换为整数
        if value.isdigit():
            config_dict[key] = int(value)
        elif value.lower() == 'true':
            config_dict[key] = True
        elif value.lower() == 'false':
            config_dict[key] = False
        else:
            config_dict[key] = value

    return config_dict


def get_config_file_path() -> str:
    """
    获取默认配置文件路径
    
    Returns:
        str: 配置文件的绝对路径
    """
    current_dir = pathlib.Path(__file__).parent.absolute()
    return os.path.join(current_dir, 'config.ini')


def load_all_config_sections(config_file_path: str = None) -> Dict[str, Dict[str, Any]]:
    """
    加载配置文件中的所有节
    
    Args:
        config_file_path (str, optional): 配置文件路径，如果不提供则使用默认路径
        
    Returns:
        Dict[str, Dict[str, Any]]: 包含所有节的配置字典
        
    Raises:
        FileNotFoundError: 当配置文件不存在时
    """
    config_parser = configparser.ConfigParser(interpolation=None)
    
    if config_file_path is None:
        config_file_path = get_config_file_path()
    
    if not os.path.exists(config_file_path):
        raise FileNotFoundError(f"配置文件不存在: {config_file_path}")
    
    config_parser.read(config_file_path, encoding='utf-8')
    
    all_config = {}
    for section_name in config_parser.sections():
        all_config[section_name] = load_config_from_ini(section_name, config_file_path)
    
    return all_config
