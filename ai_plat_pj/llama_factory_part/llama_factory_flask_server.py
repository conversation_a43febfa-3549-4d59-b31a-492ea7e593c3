from flask import Flask, request, jsonify
import logging
from minio_utils import MinioTool
from Mysql_utils import MysqlUtilPool
import json
import os
import shutil
import subprocess
import configparser
import pathlib
from finetune_utils import start_finetuning

app = Flask(__name__)


def load_config_from_ini(part):
    """从 INI 文件加载配置并转换为字典"""
    config_parser = configparser.ConfigParser(interpolation=None)  # 禁用插值以避免%字符问题
    # 获取当前文件所在目录
    current_dir = pathlib.Path(__file__).parent.absolute()
    config_file = os.path.join(current_dir, 'config.ini')

    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    config_parser.read(config_file)

    # 将指定节中的所有配置项转换为字典
    config_dict = {}
    for key, value in config_parser[part].items():
        # 尝试将数值类型的字符串转换为整数
        if value.isdigit():
            config_dict[key] = int(value)
        elif value.lower() == 'true':
            config_dict[key] = True
        elif value.lower() == 'false':
            config_dict[key] = False
        else:
            config_dict[key] = value

    return config_dict


# 从 INI 文件加载配置
config = load_config_from_ini("DEFAULT")

llama_factory_server_sqls = {
    "dataset_init_failed": "UPDATE finetune_jobs SET status='dataset_init_failed' WHERE finetune_job_id=%s",
    "model_init_failed": "UPDATE finetune_jobs SET status='model_init_failed' WHERE finetune_job_id=%s",
    "running_failed": "UPDATE finetune_jobs SET status='running_failed' WHERE finetune_job_id=%s",
    "pending": "UPDATE finetune_jobs SET status='pending' WHERE finetune_job_id=%s",
    "running": "UPDATE finetune_jobs SET status='running' WHERE finetune_job_id=%s",
    "success": "UPDATE finetune_jobs SET status='success' WHERE finetune_job_id=%s",
    "get_finetune_jobs_info": "SELECT * FROM finetune_jobs WHERE finetune_job_id=%s",
    "get_dataset_info": "SELECT * FROM datasets WHERE dataset_id=%s",
    "get_model_info": "SELECT * FROM model_registry WHERE model_registry_id=%s",
    "get_running_dev": "SELECT * FROM finetune_jobs WHERE status = 'runnning' AND finetune_server_id=%s AND is_deleted=0 ",
    "uploading": "UPDATE model_registry SET status='uploading' WHERE model_registry_id=%s",
    "uploaded": "UPDATE model_registry SET status='inited' WHERE model_registry_id=%s",
    "upload_storage_path": "UPDATE model_registry SET storage_path=%s WHERE model_registry_id=%s"
}
sql_connection = MysqlUtilPool()
minio_tool = MinioTool()


@app.route('/finetune_init', methods=['GET'])
def finetune_init():
    """
    finetune_job_id: finetune任务的id
    """
    finetune_job_id = str(request.args.get('finetune_job_id'))
    finetune_job_info = sql_connection.fetchone(llama_factory_server_sqls['get_finetune_jobs_info'],
                                                (finetune_job_id,))

    if finetune_job_info is None:
        return jsonify({"status": "failed", "message": "finetune_job_id不存在"})
    if finetune_job_info['status'] == 'success':
        return jsonify({"status": "success", "message": "该任务已经完成"})

    dataset_ids = finetune_job_info['dataset_id'].split(",")
    model_id = str(finetune_job_info['base_model_id'])
    logging.info("使用的数据集id为：" + str(dataset_ids))
    logging.info("使用的模型id为：" + model_id)

    model_info = sql_connection.fetchone(llama_factory_server_sqls['get_model_info'], (model_id,))

    if not model_info.get('storage_path'):
        return jsonify({"status": "failed", "message": "模型未初始化完成，请稍后再试"})

    dataset_json_path = config.get('dataset_path') + "/" + "dataset_info.json"
    for dataset_id in dataset_ids:
        dataset_info = sql_connection.fetchone(llama_factory_server_sqls['get_dataset_info'], (dataset_id,))
        if not dataset_info.get('storage_path'):
            return jsonify({"status": "failed", "message": "数据集未初始化完成，请稍后再试"})
        dataset_download_url = dataset_info['storage_path']
        with open(dataset_json_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        # 数据集本地不存在的情况
        if dataset_id not in data:
            try:
                download_bucket = config.get('dataset_bucket')
                download_object = dataset_download_url.split("/", 1)[1]
                download_local_path = config.get(
                    'dataset_path') + "/" + dataset_id
                # 如果dataset_path/dataset_id这个文件夹不存在，则创建
                if not os.path.exists(config.get('dataset_path') + "/" + dataset_id):
                    os.makedirs(config.get('dataset_path') + "/" + dataset_id)
                download_whole_path = download_local_path + "/" + download_object.split("/")[-1]
                logging.info(
                    "数据集下载的三个参数为：" + download_bucket + "," + download_object + "," + download_whole_path)
                minio_tool.download_file(download_bucket, download_object, download_whole_path)
                data[dataset_id] = {
                    "file_name": download_whole_path,
                }
                with open(dataset_json_path, 'w', encoding='utf-8') as file:
                    json.dump(data, file, indent=4)
            except Exception as e:
                sql_connection.execute(llama_factory_server_sqls['dataset_init_failed'], (finetune_job_id,))
                return jsonify({"status": "failed", "message": str(e)})
        else:
            logging.info("数据集已经存在本地")
        if dataset_info.get('related_data_storage_path') and dataset_info.get('related_data_custom_path'):
            logging.info("存在相关数据集")
            related_data_download_url = dataset_info['related_data_storage_path']
            related_data_custom_path = dataset_info['related_data_custom_path']
            try:
                download_bucket = config.get('datasets_react_path')
                download_object = related_data_download_url.split("/", 1)[1]

                # 如果dataset_path/related_data_custom_path这个文件夹不存在，则创建
                if not os.path.exists(related_data_custom_path):
                    os.makedirs(related_data_custom_path)
                logging.info(
                    f"相关数据集下载的三个参数为：{download_bucket},{related_data_custom_path},{download_object}")
                minio_tool.download_file(download_bucket, download_object, related_data_custom_path)
                # 解压相关数据集
                logging.info(f"解压路径为：{related_data_custom_path}")
                logging.info(f"压缩包路径为：{related_data_custom_path.split('.zip')[0]}")
                os.system(f"unzip {related_data_custom_path} -d {related_data_custom_path.split('.zip')[0]}")
                # 移除压缩包
                os.remove(related_data_custom_path)
            except Exception as e:
                sql_connection.execute(llama_factory_server_sqls['dataset_init_failed'], (finetune_job_id,))
                return jsonify({"status": "failed", "message": str(e)})
    model_download_url = model_info['storage_path']
    model_path = config.get('model_path') + "/" + model_id
    # 判断本地是否存在model_path这个文件夹
    if not os.path.exists(model_path):
        try:
            download_bucket = config.get('model_bucket')
            # 如果models/user1这个文件夹不存在，则创建
            os.makedirs(model_path)
            # 本地文件路径 models/user1/qwen.zip
            unzip_path = model_path + "/" + model_download_url.split("/")[-1]
            logging.info("模型下载的三个参数为：" + download_bucket + "," + model_download_url + "," + unzip_path)
            minio_tool.download_file(download_bucket, model_download_url.split("/", 1)[1], unzip_path)

            logging.info("解压路径为：" + unzip_path)
            # 解压模型文件
            os.system(f"unzip {unzip_path} -d {model_path}")
            # 移除压缩包
            os.remove(unzip_path)

        except Exception as e:
            if os.path.exists(model_path):
                shutil.rmtree(model_path)
            sql_connection.execute(llama_factory_server_sqls['model_init_failed'], (finetune_job_id,))
            return jsonify({"status": "failed", "message": str(e)})
    # 数据下载完成，状态置为pending
    sql_connection.execute(llama_factory_server_sqls['pending'], (finetune_job_id,))
    # 调用递归函数，直到把所有pending任务都处理完,但是要保证设备本身是空闲的
    running_tasks = sql_connection.fetchall(llama_factory_server_sqls['get_running_dev'], (config.get('dev_id'),))
    if running_tasks is None or len(running_tasks) == 0:
        start_finetuning(sql_connection=sql_connection)
        return jsonify({'status': 'success', 'message': "初始化完成"})
    else:
        return jsonify({'status': 'success', 'message': "初始化完成"})


@app.route('/finetune_export', methods=['GET'])
def finetune_export():
    """
    导出finetune模型
    finetune_job_id: finetune任务的id
    model_registry_id: finetune模型的id
    user_id: 用户的id
    """
    finetune_job_id = str(request.args.get('finetune_job_id'))
    model_registry_id = str(request.args.get('model_registry_id'))
    user_id = str(request.args.get('user_id'))
    logging.info("model_registry_id: %s", model_registry_id)
    base_model_id = sql_connection.fetchone(llama_factory_server_sqls['get_finetune_jobs_info'],
                                            params=(finetune_job_id,)).get('base_model_id')

    export_dir = os.path.join(config.get('export_dir'), finetune_job_id)
    output_dir = os.path.join(config.get('output_path'), finetune_job_id)
    params = {
        "model_name_or_path": os.path.join(config.get('model_path'), base_model_id),
        "adapter_name_or_path": output_dir,
        "export_dir": export_dir,
        "export_size": 5,
        "export_device": "cpu",
        "export_legacy_format": "false"
    }
    params_json = json.dumps(params)
    command = [
        "sh", config["finetuning_export_script_path"],
        "--conda_env_name", config["conda_env_name"],
        "--params", params_json,
        "--env", "prod",  # 或者 "prod" 根据需要
        "--outputlog", config["finetuning_output_log_path"]
    ]

    try:
        result = subprocess.run(command, check=True, text=True, capture_output=True)
        logging.info("Export executed successfully.")
        original_dir = os.getcwd()
        os.chdir(config.get('export_dir') + '/' + finetune_job_id)
        # 压缩模型文件
        os.system(f"zip -r ../{finetune_job_id}.zip ./*")
        os.chdir(original_dir)
        sql_connection.execute(llama_factory_server_sqls['uploading'], params=(model_registry_id,))
        # 上传模型文件到minio
        minio_tool.upload_file(
            local_file_path=f"{export_dir}.zip",
            bucket_name=config['model_bucket'],
            object_name=f"{user_id}/{finetune_job_id}.zip"
        )
        sql_connection.execute(llama_factory_server_sqls['uploaded'], params=(model_registry_id,))
        logging.info("finetune model export successfully.")
        sql_connection.execute(llama_factory_server_sqls['upload_storage_path'],
                               params=(
                                   f"{config['model_bucket']}/{user_id}/{finetune_job_id}.zip", model_registry_id))
        return jsonify({'status': 'success', 'message': "模型导出成功"})
    except subprocess.CalledProcessError as e:
        logging.info("An error occurred while executing the command.")
        logging.info("task_id:", finetune_job_id)
        logging.info("Error:", e.stderr)
        return jsonify({'status': 'failed', 'message': "模型导出失败"})
    finally:
        if os.path.exists(f"{export_dir}.zip"):
            os.remove(f"{export_dir}.zip")
        if os.path.exists(export_dir):
            shutil.rmtree(export_dir)


@app.route('/finetune_export_old', methods=['GET'])
def finetune_export_old():
    """
    导出finetune模型
    finetune_job_id: finetune任务的id
    model_registry_id: finetune模型的id
    user_id: 用户的id
    """
    finetune_job_id = str(request.args.get('finetune_job_id'))
    model_registry_id = str(request.args.get('model_registry_id'))
    user_id = str(request.args.get('user_id'))
    finetune_job_info = sql_connection.fetchone(llama_factory_server_sqls['get_finetune_jobs_info'],
                                                (finetune_job_id,))
    if finetune_job_info is None:
        return jsonify({"status": "failed", "message": "finetune_job_id不存在"})
    if not finetune_job_info.get('output_path'):
        return jsonify({"status": "failed", "message": "该任务输出目录为空"})
    output_path = finetune_job_info.get('output_path')
    original_dir = os.getcwd()
    os.chdir(output_path)
    os.system(f"zip -r ../{finetune_job_id}.zip ./*")
    os.chdir(original_dir)
    sql_connection.execute(llama_factory_server_sqls['uploading'], params=(model_registry_id,))
    try:
        # 上传模型文件到minio
        minio_tool.upload_file(
            local_file_path=f"{output_path}.zip",
            bucket_name=config['model_bucket'],
            object_name=f"{user_id}/{finetune_job_id}.zip"
        )
        sql_connection.execute(llama_factory_server_sqls['uploaded'], params=(model_registry_id,))
        logging.info("finetune model export successfully.")
        sql_connection.execute(llama_factory_server_sqls['upload_storage_path'],
                               params=(
                                   f"{config['model_bucket']}/{user_id}/{finetune_job_id}.zip", model_registry_id))
        return jsonify({'status': 'success', 'message': "模型导出成功"})
    except subprocess.CalledProcessError as e:
        logging.info("An error occurred while executing the command.")
        logging.info("task_id:", finetune_job_id)
        logging.info("Error:", e.stderr)
        return jsonify({'status': 'failed', 'message': "模型导出失败"})
    finally:
        if os.path.exists(f"{output_path}.zip"):
            os.remove(f"{output_path}.zip")


@app.route('/get_finetune_jobs_log', methods=['GET'])
def get_finetune_jobs_log():
    """
    获取finetune任务的日志
    finetune_job_id: finetune任务的id
    """
    finetune_job_id = str(request.args.get('finetune_job_id'))
    finetune_job_info = sql_connection.fetchone(llama_factory_server_sqls['get_finetune_jobs_info'],
                                                (finetune_job_id,))
    if finetune_job_info is None:
        return jsonify({"status": "failed", "message": "finetune_job_id不存在"})
    if not finetune_job_info.get('output_path'):
        return jsonify({"status": "failed", "message": "该任务日志目录为空"})
    if not finetune_job_info.get('log_path'):
        return jsonify({"status": "failed", "message": "该任务日志文件为空"})
    output_path = finetune_job_info.get('output_path')
    log_path = finetune_job_info.get('log_path')
    trainer_log_path = output_path + '/trainer_log.jsonl'
    if not os.path.exists(log_path):
        return jsonify({"status": "failed", "message": "日志文件不存在"})
    with open(log_path, 'r', encoding='utf-8') as logs_file:
        log_content = logs_file.read()
    trainer_log_list = []
    if not os.path.exists(trainer_log_path):
        return jsonify({"status": "success", "log_content": log_content, "trainer_log_list": trainer_log_list})
    with open(trainer_log_path, 'r', encoding='utf-8') as trainer_logs_file:
        for line in trainer_logs_file:
            trainer_log_list.append(json.loads(line.strip()))
    return jsonify({"status": "success", "log_content": log_content, "trainer_log_list": trainer_log_list})


@app.route('/remove_finetune_job', methods=['GET'])
def remove_finetune_job():
    """
    删除finetune任务
    finetune_job_id: finetune任务的id
    """
    finetune_job_id = str(request.args.get('finetune_job_id'))
    finetune_job_info = sql_connection.fetchone(llama_factory_server_sqls['get_finetune_jobs_info'],
                                                (finetune_job_id,))
    if finetune_job_info is None:
        return jsonify({"status": "failed", "message": "finetune_job_id不存在"})

    if finetune_job_info['output_path']:
        try:
            output_path = finetune_job_info['output_path']
            if os.path.exists(output_path):
                shutil.rmtree(output_path)
            return jsonify({"status": "success", "message": "删除成功"})
        except Exception as e:
            return jsonify({"status": "failed", "message": str(e)})
    else:
        return jsonify({"status": "failed", "message": "该任务输出目录为空"})


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
