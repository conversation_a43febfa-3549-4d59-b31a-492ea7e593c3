import configparser
import logging
import os
import pathlib

from minio import Minio
from minio.error import S3Error

logger = logging.getLogger(__name__)

def load_config_from_ini(part):
    """从 INI 文件加载配置并转换为字典"""
    config_parser = configparser.ConfigParser(interpolation=None)  # 禁用插值以避免%字符问题
    # 获取当前文件所在目录
    current_dir = pathlib.Path(__file__).parent.absolute()
    config_file = os.path.join(current_dir, 'config.ini')

    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    config_parser.read(config_file)

    # 将指定节中的所有配置项转换为字典
    config_dict = {}
    for key, value in config_parser[part].items():
        # 尝试将数值类型的字符串转换为整数
        if value.isdigit():
            config_dict[key] = int(value)
        elif value.lower() == 'true':
            config_dict[key] = True
        elif value.lower() == 'false':
            config_dict[key] = False
        else:
            config_dict[key] = value

    return config_dict

class MinioTool():
    def __init__(self):
        """
        初始化 Minio 客户端
        """
        minio_config = load_config_from_ini('MinIO')
        self.client = Minio(
            endpoint=minio_config.get('minio_endpoint'),
            access_key=minio_config.get('minio_access_key'),
            secret_key=minio_config.get('minio_secret_key'),
            secure=minio_config.get('minio_secure')
        )

    def upload_file(self, local_file_path, bucket_name, object_name=None):
        """
        上传本地文件到指定的 bucket 和目录

        :param local_file_path: 本地文件路径
        :param bucket_name: 目标 bucket 名称
        :param object_name: 对象名称（可选），如果未指定，则使用本地文件名
        :return: 上传结果
        """
        if object_name is None:
            object_name = os.path.basename(local_file_path)

        try:
            # 检查 bucket 是否存在，如果不存在则创建
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                logger.info(f"Bucket '{bucket_name}' 创建成功。")
            else:
                logger.info(f"Bucket '{bucket_name}' 已存在。")

            # 上传文件
            self.client.fput_object(
                bucket_name=bucket_name,
                object_name=object_name,
                file_path=local_file_path
            )
            logger.info(f"文件 '{local_file_path}' 上传到 bucket '{bucket_name}' 成功。")
            return True
        except S3Error as e:
            logger.info(f"上传文件失败: {e}")
            raise e

    def download_file(self, bucket_name, object_name, local_file_path):
        """
        从 Minio 下载文件到本地路径

        :param bucket_name: 源 bucket 名称
        :param object_name: 对象名称
        :param local_file_path: 本地文件保存路径
        :return: 下载结果
        """
        try:
            self.client.fget_object(
                bucket_name=bucket_name,
                object_name=object_name,
                file_path=local_file_path
            )
            logger.info(f"文件 '{object_name}' 从 bucket '{bucket_name}' 下载到 '{local_file_path}' 成功。")
            return True
        except S3Error as e:
            logger.info(f"下载文件失败: {e}")
            raise e

    def make_bucket(self, bucket_name):
        """
        新建一个 bucket

        :param bucket_name: 要创建的 bucket 名称
        :return: 创建结果
        """
        try:
            self.client.make_bucket(bucket_name)
            logger.info(f"Bucket '{bucket_name}' 创建成功。")
            return True
        except S3Error as e:
            logger.info(f"创建 bucket 失败: {e}")
            raise e

    def list_objects(self, bucket_name):
        """
        列出 bucket 内的所有对象

        :param bucket_name: bucket 名称
        :return: 对象列表
        """
        try:
            objects = self.client.list_objects(bucket_name, recursive=True)
            object_list = [obj.object_name for obj in objects]
            return object_list
        except S3Error as e:
            logger.info(f"列出对象失败: {e}")
            raise e

    def remove_object(self, bucket_name, object_name):
        """
        删除一个对象

        :param bucket_name: bucket 名称
        :param object_name: 对象名称
        :return: 删除结果
        """
        try:
            self.client.remove_object(bucket_name, object_name)
            logger.info(f"对象 '{object_name}' 删除成功。")
            return True
        except S3Error as e:
            logger.info(f"删除对象失败: {e}")
            raise e

    def remove_bucket(self, bucket_name):
        """
        删除一个 bucket

        :param bucket_name: bucket 名称
        :return: 删除结果
        """
        try:
            self.client.remove_bucket(bucket_name)
            logger.info(f"Bucket '{bucket_name}' 删除成功。")
            return True
        except S3Error as e:
            logger.info(f"删除 bucket 失败: {e}")
            raise e


# 示例用法
if __name__ == "__main__":
    # 初始化 Minio 客户端
    # 示例用法已修改：现在使用从配置文件加载的方式初始化MinioTool
    # MinioTool的初始化参数已改为从config.ini文件中读取
    minio_tool = MinioTool()

    # 上传文件
    local_path = "/path/to/local/file.txt"
    bucket_path = "my-bucket"
    object_path = "documents/file.txt"
    minio_tool.upload_file(local_path, bucket_path, object_path)

    # 下载文件
    download_bucket = "my-bucket"
    download_object = "documents/file.txt"
    download_local_path = "/path/to/downloaded/file.txt"
    minio_tool.download_file(download_bucket, download_object, download_local_path)

    # 新建 bucket
    new_bucket = "new-bucket"
    minio_tool.make_bucket(new_bucket)

    # 列出对象
    objects = minio_tool.list_objects(new_bucket)
    logger.info(f"Objects in '{new_bucket}': {objects}")

    # 删除对象
    minio_tool.remove_object(new_bucket, "documents/file.txt")

    # 删除 bucket
    minio_tool.remove_bucket(new_bucket)
