import configparser
import logging
import os
import pathlib

import pymysql
from dbutils.pooled_db import PooledDB

logger = logging.getLogger(__name__)

def load_config_from_ini(part):
    """从 INI 文件加载配置并转换为字典"""
    config_parser = configparser.ConfigParser(interpolation=None)  # 禁用插值以避免%字符问题
    # 获取当前文件所在目录
    current_dir = pathlib.Path(__file__).parent.absolute()
    config_file = os.path.join(current_dir, 'config.ini')

    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    config_parser.read(config_file)

    # 将指定节中的所有配置项转换为字典
    config_dict = {}
    for key, value in config_parser[part].items():
        # 尝试将数值类型的字符串转换为整数
        if value.isdigit():
            config_dict[key] = int(value)
        else:
            config_dict[key] = value

    return config_dict

class MysqlUtilPool():
    def __init__(self):
        db_config = load_config_from_ini('MySQL')

        self.POOL = PooledDB(
            creator=pymysql,  # 使用链接数据库的模块
            maxconnections=20,  # 连接池允许的最大连接数，0和None表示不限制连接数
            mincached=2,  # 初始化时，链接池中至少创建的空闲的链接，0表示不创建
            maxcached=10,  # 链接池中最多闲置的链接，0和None不限制
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待。True，等待；False，不等待然后报错
            maxusage=None,  # 一个链接最多被重复使用的次数，None表示无限制
            host=db_config['mysql_host'],
            user=db_config['mysql_user'],
            password=db_config['mysql_password'],
            database=db_config['mysql_database'],
            port=int(db_config['mysql_port']),  # 确保端口是整数类型
            charset=db_config['mysql_charset']
        )
