import subprocess
import json
import os
import shutil
import logging
import requests

from config_utils import load_config_from_ini

logger = logging.getLogger(__name__)

finetune_sql = {
    "get_dev_status": "SELECT * FROM finetune_jobs WHERE finetune_server_id=%s AND status='pending' AND is_deleted=0 ORDER BY created_at DESC",
    "set_status_running": "UPDATE finetune_jobs SET status='running' WHERE finetune_job_id=%s",
    "set_status_success": "UPDATE finetune_jobs SET status='success' WHERE finetune_job_id=%s",
    "set_status_running_failed": "UPDATE finetune_jobs SET status='running_failed' WHERE finetune_job_id=%s",
    "set_output_path": "UPDATE finetune_jobs SET output_path=%s WHERE finetune_job_id=%s",
    "set_log_path": "UPDATE finetune_jobs SET log_path=%s WHERE finetune_job_id=%s",
    "set_swanlabs_job_id": "UPDATE finetune_jobs SET swanlabs_job_id=%s WHERE finetune_job_id=%s"
}


##TODO
def start_finetuning(sql_connection, config):
    logger.info("开始微调任务...")
    waiting_tasks = sql_connection.fetchall(finetune_sql["get_dev_status"], (config["dev_id"],))
    if waiting_tasks is None or len(waiting_tasks) == 0:
        logger.info("数据库中未找到任务，正在退出...")
        return "finished"
    exec_task = waiting_tasks[0]
    logger.info("开始执行任务，任务ID: ", exec_task["finetune_job_id"])
    model_path = config["model_path"] + "/" + str(exec_task["base_model_id"])
    dataset_info = exec_task["dataset_id"]
    # 此处不确定是否可以引入绝对路径
    output_path = config["output_path"] + "/" + str(exec_task["finetune_job_id"])
    log_path = config["finetuning_output_log_path"] + "/" + "para_log_" + str(
        exec_task["finetune_job_id"]) + ".log"
    # 更新output_path
    sql_connection.execute(finetune_sql["set_output_path"], (output_path, exec_task["finetune_job_id"]))
    sql_connection.execute(finetune_sql["set_log_path"], (log_path, exec_task["finetune_job_id"]))
    # 如果 output_path 已经存在，则删除
    if os.path.exists(output_path):
        shutil.rmtree(output_path)
    os.makedirs(output_path, exist_ok=True)

    params = json.loads(exec_task["hyperparameters"])
    params["model_name_or_path"] = model_path
    params["dataset"] = dataset_info
    params["output_dir"] = output_path
    params["dataset_dir"] = config["dataset_path"]
    params_json = json.dumps(params)
    # 构建命令
    command = [
        "bash", config["finetuning_script_path"],
        "--conda_env_name", config["conda_env_name"],
        "--params", params_json,
        "--env", "prod",  # 或者 "prod" 根据需要
        "--outputlog", config["finetuning_output_log_path"],
        "--task_id", str(exec_task["finetune_job_id"])
    ]
    sql_connection.execute(finetune_sql["set_status_running"], (exec_task["finetune_job_id"],))
    # 运行命令
    try:
        result = subprocess.run(command, check=True, text=True, capture_output=True)
        logger.info("命令执行成功。")
        
        # 调用 API 接口并获取 swanlab 实验 ID
        try:
            config2 = load_config_from_ini("Swanlab")
            # 从配置中读取 Swanlab 的主机和端口
            swanlab_host = config2.get("swanlab_host")
            swanlab_port = config2.get("swanlab_port")
            api_url = f"http://{swanlab_host}:{swanlab_port}/api/v1/project"
            
            response = requests.get(api_url)
            swanlab_job_id = None
            
            if response.status_code == 0:
                logger.info("API 调用成功")
                data = response.json()
                
                # 解析 experiments 数据
                experiments = data.get("data", {}).get("experiments", [])
                finetune_job_name = "finetune_job_" + str(exec_task["job_id"])
                
                # 根据名称查找对应的实验 ID
                for experiment in experiments:
                    if experiment.get("name") == finetune_job_name:
                        swanlab_job_id = experiment.get("id")
                        break
                
                if swanlab_job_id:
                    logger.info(f"找到 swanlab 任务 ID: {swanlab_job_id}")
                    # 保存 swanlab_job_id 到数据库
                    sql_connection.execute(finetune_sql["set_swanlabs_job_id"], (swanlab_job_id, exec_task["finetune_job_id"]))
                else:
                    logger.info(f"未找到匹配的实验，名称: {finetune_job_name}")
            else:
                logger.info(f"API 调用失败，状态码: {response.status_code}")
        except Exception as api_error:
            logger.info(f"调用 API 或解析响应时出错: {api_error}")
        
        sql_connection.execute(finetune_sql["set_status_success"], (exec_task["finetune_job_id"],))
        start_finetuning(sql_connection, config)
    except subprocess.CalledProcessError as e:
        logger.info("执行命令时发生错误。")
        logger.info("任务ID:", exec_task["finetune_job_id"])
        logger.info("错误信息:", e.stderr)
        sql_connection.execute(finetune_sql["set_status_running_failed"], (exec_task["finetune_job_id"],))
        start_finetuning(sql_connection, config)